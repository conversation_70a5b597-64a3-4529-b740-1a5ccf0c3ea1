@extends('layouts.app')

@section('content')
<!-- Category Header -->
<section class="py-5" style="background: linear-gradient(135deg, var(--primary-dark-green) 0%, var(--secondary-dark-green) 100%); color: white;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-3">
                    @if($category->image)
                        <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                    @else
                        <div class="bg-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <span class="text-muted">No Image</span>
                        </div>
                    @endif
                    <div>
                        <h1 class="display-5 fw-bold mb-2">{{ $category->name }}</h1>
                        <p class="lead mb-0">{{ $category->description ?? 'Discover amazing deals in this category' }}</p>
                    </div>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white-50">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('categories.index') }}" class="text-white-50">Categories</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ $category->name }}</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-4 text-end">
                <div class="row text-center">
                    <div class="col-6">
                        <h3>{{ $stores->total() }}</h3>
                        <small>Stores</small>
                    </div>
                    <div class="col-6">
                        <h3>{{ $deals->total() }}</h3>
                        <small>Deals</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Deals Section -->


<!-- Stores Section -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-success">{{ $category->name }} Stores</h2>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success btn-sm" id="gridView">
                    <i class="fas fa-th"></i> Grid
                </button>
                <button class="btn btn-outline-success btn-sm" id="listView">
                    <i class="fas fa-list"></i> List
                </button>
            </div>
        </div>

        @if($stores->count() > 0)
            <div class="row g-4" id="storesContainer">
                @foreach($stores as $store)
                <div class="col-md-6 col-lg-4 store-item">
                    <div class="card h-100">
                        <div class="position-relative">
                            @if($store->image)
                                <img src="{{ asset('storage/' . $store->image) }}" class="card-img-top" alt="{{ $store->name }}" style="height: 200px; object-fit: cover;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-store fa-3x text-muted"></i>
                                </div>
                            @endif
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-success">{{ $store->deals->count() }} Deals</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ $store->name }}</h5>
                            <p class="card-text text-muted">{{ Str::limit($store->description, 100) }}</p>
                            @if($store->address)
                                <p class="card-text"><small class="text-muted"><i class="fas fa-map-marker-alt"></i> {{ $store->address }}</small></p>
                            @endif

                            <!-- Store's Latest Deals -->
                            @if($store->deals->count() > 0)
                                <div class="mt-3">
                                    <h6 class="text-success">Latest Deals:</h6>
                                    @foreach($store->deals->take(2) as $deal)
                                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                            <div>
                                                <small class="fw-bold">{{ Str::limit($deal->title, 30) }}</small>
                                                <div class="text-success small">{{ $deal->discount_percentage }}% OFF</div>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted text-decoration-line-through">PKR {{ number_format($deal->original_price, 0) }}</small>
                                                <div class="fw-bold text-success small">PKR {{ number_format($deal->discounted_price, 0) }}</div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        <div class="card-footer bg-transparent">
                         
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $stores->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                <h5>No Stores Found</h5>
                <p class="text-muted">No stores are currently offering deals in this category.</p>
                <a href="{{ route('home') }}" class="btn btn-success">
                    <i class="fas fa-home"></i> Back to Home
                </a>
            </div>
        @endif
    </div>
</section>

<!-- All Deals Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-success mb-4">All {{ $category->name }} Deals</h2>

        @if($deals->count() > 0)
            <div class="row g-4">
                @foreach($deals as $deal)
                <div class="col-md-6">
                    <div class="card deal-card h-100">
                        <div class="row g-0">
                            <div class="col-md-4 position-relative">
                                <div class="save-badge position-absolute top-0 start-0 m-2">{{ $deal->discount_percentage }}% OFF</div>
                                @if($deal->image)
                                    <img src="{{ asset('storage/' . $deal->image) }}" class="img-fluid rounded-start h-100" alt="{{ $deal->title }}" style="object-fit: cover;">
                                @else
                                    <div class="bg-secondary d-flex align-items-center justify-content-center text-white h-100 rounded-start">
                                        <i class="fas fa-image fa-2x"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="col-md-8">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $deal->title }}</h5>
                                    <p class="text-muted small">{{ $deal->store->name }}</p>
                                    <p class="card-text">{{ Str::limit($deal->description, 80) }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="text-decoration-line-through text-muted">PKR {{ number_format($deal->original_price, 2) }}</span>
                                            <div class="fw-bold text-success">PKR {{ number_format($deal->discounted_price, 2) }}</div>
                                            <small class="text-danger">-{{ $deal->discount_percentage }}% Limited time</small>
                                        </div>
                                        <div class="text-end">
                                            <div class="text-warning">
                                                <i class="fas fa-star"></i> 4.7 (1077)
                                            </div>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-success btn-sm mt-2 w-100" onclick="showDealModal({{ $deal->id }})">
                                        <i class="fas fa-eye"></i> View Deal Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $deals->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5>No Deals Found</h5>
                <p class="text-muted">No deals are currently available in this category.</p>
                <a href="{{ route('home') }}" class="btn btn-success">
                    <i class="fas fa-home"></i> Back to Home
                </a>
            </div>
        @endif
    </div>
</section>

<script>
function showDealModal(dealId) {
    // Deal modal functionality (same as homepage)
    console.log('Show deal modal for deal ID:', dealId);
}

// View toggle functionality
document.getElementById('gridView').addEventListener('click', function() {
    document.getElementById('storesContainer').className = 'row g-4';
    document.querySelectorAll('.store-item').forEach(item => {
        item.className = 'col-md-6 col-lg-4 store-item';
    });
    this.classList.add('active');
    document.getElementById('listView').classList.remove('active');
});

document.getElementById('listView').addEventListener('click', function() {
    document.getElementById('storesContainer').className = 'row g-2';
    document.querySelectorAll('.store-item').forEach(item => {
        item.className = 'col-12 store-item';
    });
    this.classList.add('active');
    document.getElementById('gridView').classList.remove('active');
});
</script>
@endsection
